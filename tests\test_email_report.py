#!/usr/bin/env python3
"""
Email Report Test Script

This script tests the email notification functionality of the TNGD backup system.
It demonstrates how to send various types of email reports and notifications.

Usage:
    python test_email_report.py [--test-config] [--send-sample-report] [--send-error-notification]
"""

import sys
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.config_manager import ConfigManager
from utils.notification_service import NotificationService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_email_configuration():
    """Test email configuration by sending a test email."""
    print("🧪 Testing Email Configuration...")
    print("=" * 50)
    
    try:
        # Initialize configuration manager
        config_manager = ConfigManager()
        
        # Initialize notification service
        notification_service = NotificationService(config_manager)
        
        # Test email configuration
        success = notification_service.test_email_configuration()
        
        if success:
            print("✅ Email configuration test successful!")
            print("📧 Check your email inbox for the test message.")
        else:
            print("❌ Email configuration test failed!")
            print("🔍 Check the logs for error details.")
        
        return success
        
    except Exception as e:
        print(f"❌ Email configuration test failed with error: {str(e)}")
        logger.error(f"Email configuration test error: {str(e)}")
        return False

def send_sample_backup_report():
    """Send a sample backup report email."""
    print("📊 Sending Sample Backup Report...")
    print("=" * 50)
    
    try:
        # Initialize configuration manager
        config_manager = ConfigManager()
        
        # Initialize notification service
        notification_service = NotificationService(config_manager)
        
        # Create sample backup results
        sample_results = [
            {
                'table_name': 'my.app.tngd.waf',
                'target_date': '2025-06-24',
                'status': 'completed',
                'file_size_mb': 125.5,
                'duration_seconds': 45.2,
                'upload_path': 'Devo/June/week 4/2025-06-24/my.app.tngd.waf_2025-06-24.tar.gz',
                'start_time': datetime.now() - timedelta(minutes=2),
                'end_time': datetime.now()
            },
            {
                'table_name': 'my.app.tngd.dns',
                'target_date': '2025-06-24',
                'status': 'completed',
                'file_size_mb': 89.3,
                'duration_seconds': 32.1,
                'upload_path': 'Devo/June/week 4/2025-06-24/my.app.tngd.dns_2025-06-24.tar.gz',
                'start_time': datetime.now() - timedelta(minutes=1, seconds=30),
                'end_time': datetime.now() - timedelta(seconds=30)
            },
            {
                'table_name': 'my.app.tngd.firewall',
                'target_date': '2025-06-24',
                'status': 'failed',
                'error': 'Connection timeout to Devo API',
                'file_size_mb': 0,
                'duration_seconds': 120.0,
                'upload_path': '',
                'start_time': datetime.now() - timedelta(minutes=3),
                'end_time': datetime.now() - timedelta(minutes=1)
            }
        ]
        
        # Send backup report
        success = notification_service.send_backup_report(sample_results, "daily")
        
        if success:
            print("✅ Sample backup report sent successfully!")
            print("📧 Check your email inbox for the backup report.")
        else:
            print("❌ Failed to send sample backup report!")
            print("🔍 Check the logs for error details.")
        
        return success
        
    except Exception as e:
        print(f"❌ Sample backup report failed with error: {str(e)}")
        logger.error(f"Sample backup report error: {str(e)}")
        return False

def send_sample_error_notification():
    """Send a sample error notification email."""
    print("🚨 Sending Sample Error Notification...")
    print("=" * 50)
    
    try:
        # Initialize configuration manager
        config_manager = ConfigManager()
        
        # Initialize notification service
        notification_service = NotificationService(config_manager)
        
        # Create sample error details
        error_details = {
            'error_type': 'ConnectionError',
            'error_message': 'Failed to connect to Devo API after 3 retries',
            'context': 'Daily backup operation for table my.app.tngd.waf'
        }
        
        # Send error notification
        success = notification_service.send_error_notification(
            error_details, 
            "Daily backup process"
        )
        
        if success:
            print("✅ Sample error notification sent successfully!")
            print("📧 Check your email inbox for the error notification.")
        else:
            print("❌ Failed to send sample error notification!")
            print("🔍 Check the logs for error details.")
        
        return success
        
    except Exception as e:
        print(f"❌ Sample error notification failed with error: {str(e)}")
        logger.error(f"Sample error notification error: {str(e)}")
        return False

def display_email_settings():
    """Display current email settings (without sensitive information)."""
    print("⚙️ Current Email Settings...")
    print("=" * 50)
    
    try:
        # Initialize configuration manager
        config_manager = ConfigManager()
        
        # Get email settings
        email_settings = config_manager.get_email_settings()
        
        print(f"📧 SMTP Server: {email_settings.get('smtp_server', 'Not configured')}")
        print(f"🔌 SMTP Port: {email_settings.get('smtp_port', 'Not configured')}")
        print(f"📤 From Email: {email_settings.get('email_from', 'Not configured')}")
        print(f"📥 To Email: {email_settings.get('email_to', 'Not configured')}")
        print(f"🔒 Password Configured: {'Yes' if email_settings.get('smtp_password') else 'No'}")
        print(f"🧪 Mock Mode: {'Enabled' if email_settings.get('mock_mode', True) else 'Disabled'}")
        
        if email_settings.get('mock_mode', True):
            print("\n⚠️  Mock mode is enabled - emails will be logged but not actually sent.")
            print("   To send real emails, set SMTP_MOCK_MODE=false in your .env file.")
        
    except Exception as e:
        print(f"❌ Failed to display email settings: {str(e)}")
        logger.error(f"Email settings display error: {str(e)}")

def main():
    """Main function to handle command line arguments and run tests."""
    parser = argparse.ArgumentParser(
        description="Test TNGD Backup System Email Functionality",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python test_email_report.py --test-config
    python test_email_report.py --send-sample-report
    python test_email_report.py --send-error-notification
    python test_email_report.py --all
        """
    )
    
    parser.add_argument('--test-config', action='store_true',
                       help='Test email configuration by sending a test email')
    parser.add_argument('--send-sample-report', action='store_true',
                       help='Send a sample backup report email')
    parser.add_argument('--send-error-notification', action='store_true',
                       help='Send a sample error notification email')
    parser.add_argument('--all', action='store_true',
                       help='Run all email tests')
    parser.add_argument('--show-settings', action='store_true',
                       help='Display current email settings')
    
    args = parser.parse_args()
    
    # If no arguments provided, show help and settings
    if not any(vars(args).values()):
        parser.print_help()
        print("\n")
        display_email_settings()
        return
    
    print("🚀 TNGD Backup System - Email Report Test")
    print("=" * 60)
    
    # Show settings if requested
    if args.show_settings:
        display_email_settings()
        print()
    
    success_count = 0
    total_tests = 0
    
    # Run tests based on arguments
    if args.test_config or args.all:
        total_tests += 1
        if test_email_configuration():
            success_count += 1
        print()
    
    if args.send_sample_report or args.all:
        total_tests += 1
        if send_sample_backup_report():
            success_count += 1
        print()
    
    if args.send_error_notification or args.all:
        total_tests += 1
        if send_sample_error_notification():
            success_count += 1
        print()
    
    # Display summary if tests were run
    if total_tests > 0:
        print("=" * 60)
        print(f"📊 Test Summary: {success_count}/{total_tests} tests passed")
        
        if success_count == total_tests:
            print("🎉 All email tests completed successfully!")
        else:
            print(f"⚠️  {total_tests - success_count} test(s) failed. Check logs for details.")
        
        return success_count == total_tests
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        logger.error(f"Unexpected error in main: {str(e)}")
        sys.exit(1)
